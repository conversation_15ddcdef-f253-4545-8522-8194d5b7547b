<template>
  <view class="check-table-box">
    <wd-popup
      v-model="popupShow"
      position="bottom"
      custom-style="height: 85vh;border-radius: 32rpx;"
      @close="handleClose"
    >
      <view class="check-table">
        <view class="popup-title">
          <text>检查表详情</text>
          <wd-icon
            class="popup-close"
            name="close"
            size="32rpx"
            color="#999"
            @click="handleClose"
          ></wd-icon>
        </view>

        <!-- 使用 z-paging 虚拟列表替换原来的折叠列表 -->
        <view class="list">
          <z-paging
            ref="pagingRef"
            v-model="displayItems"
            @query="loadCheckItems"
            :default-page-size="20"
            :preload-page="3"
            :auto="false"
            class="check-items-paging"
            use-virtual-list
            :force-close-inner-list="true"
            :show-refresher="false"
            :show-loading-more="true"
          >
            <!-- 虚拟列表项 -->
            <view v-for="item in displayItems" :key="item.uniqueId" :id="`zp-id-${item.uniqueId}`">
              <!-- 检查类别标题 -->
              <view v-if="item.type === 'category'" class="category-header">
                <view class="category-header-content">
                  <view class="header-title">
                    <wd-img class="header-title-icon" :src="dangerIcon"></wd-img>
                    <text>{{ `检查类别${item.categoryIndex + 1}: ${item.checkContent}` }}</text>
                  </view>
                </view>
              </view>

              <!-- 检查内容项 -->
              <view v-else-if="item.type === 'item'" class="check-item">
                {{ `检查内容${item.itemIndex + 1}: ${item.checkDetail}` }}
              </view>
            </view>
          </z-paging>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dangerIcon from '../../assets/dangerIcon.png'
import { getCheckTableDetail } from '../../fetchData'

defineOptions({ name: 'CheckTablePopup' })

interface IProps {
  show: boolean
  id: string
}
const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: '',
})

const emits = defineEmits(['update:show'])
const popupShow = computed({
  get: () => {
    return props.show
  },
  set: (v) => {
    emits('update:show', v)
  },
})

const handleClose = () => {
  popupShow.value = false
}

// 虚拟列表相关变量
const pagingRef = ref()
const allItems = ref<any[]>([]) // 所有扁平化的检查项
const displayItems = ref<any[]>([]) // 当前显示的检查项
const originalData = ref<any[]>([]) // 原始数据

// 数据扁平化函数
const flattenCheckData = (checkAreaList: any[]) => {
  if (!checkAreaList || !checkAreaList.length) return []

  const flattened: any[] = []

  checkAreaList.forEach((categoryItem, categoryIndex) => {
    // 添加检查类别标题
    flattened.push({
      type: 'category',
      uniqueId: `category-${categoryIndex}`,
      categoryIndex,
      checkContent: categoryItem.checkContent,
      originalCategoryItem: categoryItem,
    })

    // 添加检查内容项
    if (categoryItem.childCheckAreaList && categoryItem.childCheckAreaList.length > 0) {
      categoryItem.childCheckAreaList.forEach((checkItem, itemIndex) => {
        flattened.push({
          type: 'item',
          uniqueId: `item-${categoryIndex}-${itemIndex}`,
          categoryIndex,
          itemIndex,
          checkDetail: checkItem.checkDetail,
          originalCheckItem: checkItem,
          originalCategoryItem: categoryItem,
        })
      })
    }
  })

  return flattened
}

// 分页加载函数
const loadCheckItems = (pageNo: number, pageSize: number) => {
  console.log('loadCheckItems', pageNo, pageSize)

  if (pageNo === 1) {
    // 第一页，初始化所有数据
    allItems.value = flattenCheckData(originalData.value)
  }

  const startIndex = (pageNo - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = allItems.value.slice(startIndex, endIndex)

  // 使用 setTimeout 确保数据处理完成
  setTimeout(() => {
    pagingRef.value?.completeByTotal(pageData, allItems.value.length)
  }, 50)
}

// 获取数据
function getData() {
  getCheckTableDetail(props.id).then(({ code, data }) => {
    if (code === 'success' && data.checkAreaList) {
      originalData.value = data.checkAreaList
      // 触发虚拟列表加载
      pagingRef.value?.reload()
    }
  })
}

// 监听 props.id 变化，重新获取数据
watchEffect(() => {
  if (props.id && props.show) {
    getData()
  }
})

// 监听弹窗显示状态，确保每次打开都能正确初始化
watch(
  () => props.show,
  (newShow) => {
    if (newShow && props.id) {
      // 弹窗打开时，重新加载数据
      nextTick(() => {
        getData()
      })
    }
  },
)
</script>

<style scoped lang="scss">
.check-table-box {
  position: fixed;
  z-index: 9999;
}
.check-table {
  width: 100%;
  height: 100%;

  .popup-title {
    @apply relative w-full flex items-center justify-center;
    height: 110rpx;
    font-size: 32rpx;
    line-height: 2em;
    font-weight: 700;
    border: 1px solid #eee;
  }
  .popup-close {
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
  }

  .list {
    height: calc(100% - 110rpx);
  }

  .category-header {
    background: #fff;
    padding: 24rpx 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .category-header-content {
      @apply w-full flex flex-row flex-nowrap items-center justify-between;

      .header-title {
        @apply flex flex-row flex-nowrap items-center;
        gap: 20rpx;
        font-size: 32rpx;
        line-height: 1.5em;
        color: #333;
        font-weight: 700;
      }
      .header-title-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .check-item {
    font-size: 28rpx;
    line-height: 1.5em;
    color: #333;
    background: #f7f7f7;
    border-radius: 12rpx;
    padding: 24rpx 32rpx;
    margin: 16rpx 32rpx;
    &:last-of-type {
      margin-bottom: 32rpx;
    }
  }
}

// z-paging 样式调整
.check-items-paging {
  height: 100%;
}

.check-items-paging :deep(.z-paging-content) {
  background: #fff;
}

.check-items-paging :deep(.z-paging-content-fixed) {
  background: #fff;
}
</style>
