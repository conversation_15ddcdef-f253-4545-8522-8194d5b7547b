<template>
  <view class="check-table-box">
    <wd-popup
      v-model="popupShow"
      position="bottom"
      custom-style="height: 85vh;border-radius: 32rpx 32rpx 0 0;"
      @close="handleClose"
    >
      <view class="check-table">
        <view class="popup-title">
          <text>检查表详情</text>
          <wd-icon
            class="popup-close"
            name="close"
            size="32rpx"
            color="#999"
            @click="handleClose"
          ></wd-icon>
        </view>

        <view class="list">
          <z-paging
            ref="pagingRef"
            v-model="displayItems"
            @query="loadCheckItems"
            :default-page-size="50"
            :auto="false"
            class="check-items-paging"
            use-virtual-list
            :force-close-inner-list="true"
            :use-page-scroll="false"
            :fixed="false"
          >
            <view v-for="item in displayItems" :key="item.uniqueId" :id="`zp-id-${item.uniqueId}`">
              <view v-if="item.type === 'category'" class="category-header">
                <view class="category-header-content">
                  <view class="header-title">
                    <wd-img class="header-title-icon" :src="dangerIcon"></wd-img>
                    <text>{{ `检查类别${item.categoryIndex + 1}: ${item.checkContent}` }}</text>
                  </view>
                </view>
              </view>

              <view v-else-if="item.type === 'item'" class="check-item">
                {{ `检查内容${item.itemIndex + 1}: ${item.checkDetail}` }}
              </view>
            </view>
          </z-paging>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dangerIcon from '../../assets/dangerIcon.png'
import { getCheckTableDetail } from '../../fetchData'

defineOptions({ name: 'CheckTablePopup' })

interface IProps {
  show: boolean
  id: string
}
const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: '',
})

const emits = defineEmits(['update:show'])
const popupShow = computed({
  get: () => props.show,
  set: (v) => emits('update:show', v),
})

const handleClose = () => {
  popupShow.value = false
}

const pagingRef = ref()
const allItems = ref<any[]>([])
const displayItems = ref<any[]>([])
const originalData = ref<any[]>([])

// 使用 z-paging hooks
// useZPaging(pagingRef)

const flattenCheckData = (checkAreaList: any[]) => {
  if (!checkAreaList?.length) return []

  const flattened: any[] = []

  checkAreaList.forEach((categoryItem, categoryIndex) => {
    flattened.push({
      type: 'category',
      uniqueId: `category-${categoryIndex}`,
      categoryIndex,
      checkContent: categoryItem.checkContent,
      originalCategoryItem: categoryItem,
    })

    categoryItem.childCheckAreaList?.forEach((checkItem, itemIndex) => {
      flattened.push({
        type: 'item',
        uniqueId: `item-${categoryIndex}-${itemIndex}`,
        categoryIndex,
        itemIndex,
        checkDetail: checkItem.checkDetail,
        originalCheckItem: checkItem,
        originalCategoryItem: categoryItem,
      })
    })
  })

  return flattened
}

const loadCheckItems = (pageNo: number, pageSize: number) => {
  if (pageNo === 1) {
    allItems.value = flattenCheckData(originalData.value)
  }

  const startIndex = (pageNo - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = allItems.value.slice(startIndex, endIndex)

  setTimeout(() => {
    pagingRef.value?.completeByTotal(pageData, allItems.value.length)
  }, 50)
}

const getData = () => {
  getCheckTableDetail(props.id).then(({ code, data }) => {
    if (code === 'success' && data.checkAreaList) {
      originalData.value = data.checkAreaList
      nextTick(() => {
        pagingRef.value?.reload()
      })
    }
  })
}

watchEffect(() => {
  if (props.id && props.show) getData()
})
</script>

<style scoped lang="scss">
.check-table-box {
  position: fixed;
  z-index: 9999;
}

.check-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-title {
    flex-shrink: 0;
    @apply relative w-full flex items-center justify-center;
    height: 110rpx;
    font-size: 32rpx;
    line-height: 2em;
    font-weight: 700;
    border-bottom: 1px solid #eee;
    background: #fff;
  }

  .popup-close {
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
  }

  .list {
    flex: 1;
    min-height: 0;
    overflow: hidden;
    position: relative;
    height: calc(85vh - 110rpx); /* 弹框高度减去标题高度 */

    .check-items-paging {
      height: 100% !important;
      position: relative !important;
    }

    // 确保虚拟列表内容正确显示在弹框内
    :deep(.z-paging-content) {
      position: relative !important;
      height: 100% !important;
    }

    :deep(.z-paging-content-fixed) {
      position: relative !important;
      top: auto !important;
      left: auto !important;
      height: 100% !important;
    }

    :deep(.z-paging-virtual-list) {
      height: 100% !important;
    }

    // 确保虚拟列表容器不会超出弹框范围
    :deep(.z-paging) {
      height: 100% !important;
      max-height: 100% !important;
    }

    // 修复虚拟列表滚动容器
    :deep(.z-paging-scroll-view) {
      height: 100% !important;
      max-height: 100% !important;
    }
  }
}

.category-header {
  background: #fff;
  padding: 24rpx 32rpx;

  .category-header-content {
    @apply w-full flex flex-row flex-nowrap items-center justify-between;

    .header-title {
      @apply flex flex-row flex-nowrap items-center;
      gap: 20rpx;
      font-size: 32rpx;
      line-height: 1.5em;
      color: #333;
      font-weight: 700;
    }

    .header-title-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }
  }
}

.check-item {
  font-size: 28rpx;
  line-height: 1.6em;
  color: #333;
  border-radius: 12rpx;
  margin: 5rpx 32rpx;

  &:last-of-type {
    margin-bottom: 32rpx;
  }
}
</style>
