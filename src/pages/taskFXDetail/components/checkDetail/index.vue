<template>
  <z-paging
    ref="paging"
    :fixed="false"
    :auto="false"
    v-model="listData"
    loading-more-no-more-text="我也是有底线的！"
    @query="getDataList"
  >
    <BaseInfo />
    <CheckBar @action="actionHandle" />

    <view class="container">
      <view v-if="listData.length > 0">
        <CheckPointInfo
          v-for="(item, index) in listData"
          :key="index"
          :info="item"
          @action="actionHandle"
        />
      </view>
      <template v-else>
        <EmptyComp :style="{ marginTop: '100rpx' }" text="暂无数据" />
      </template>
    </view>
  </z-paging>

  <CheckTablePopup v-model:show="checkTableShow" :id="checkTableId" />

  <PreViewImgs v-model:show="previewImgShow" :index="imgIndex" :imgs="previewImgs" />
</template>

<script lang="ts" setup>
import BaseInfo from './baseInfo.vue'
import CheckBar from './checkBar.vue'
import CheckPointInfo from './checkPointInfo.vue'
import EmptyComp from '@/components/empty/index.vue'
import CheckTablePopup from './checkTablePopup.vue'
import PreViewImgs from '@/components/previewImg/index.vue'
import { ACTION } from '../../constant'
import { DetailSerivce } from '../../detailService'
import { pageCheckPointList } from '../../fetchData'

defineOptions({ name: 'TaskFXCheckDetail' })

let routerParams: any = null

const paging = ref()
const listData = ref([])

const checkInState = ref('')

function getDataList(pageNo, pageSize) {
  console.log(pageNo, pageSize)

  if (pageNo === 1) {
    listData.value = []
  }
  uni.showLoading({
    mask: true,
  })
  pageCheckPointList({
    taskId: routerParams.taskId,
    checkInState: checkInState.value,
    pageNo,
    pageSize,
  })
    .then(({ code, data }) => {
      if (code === 'success' && data.rows) {
        paging.value.completeByTotal(data.rows, data.total)
      }
    })
    .catch(() => {
      paging.value.complete(false)
    })
    .finally(() => {
      uni.hideLoading()
    })
}

// checkTable
const checkTableShow = ref(false)
const checkTableId = ref('')

// preview-img
const previewImgShow = ref(false)
const previewImgs = ref([])
const imgIndex = ref(0)

function actionHandle(res) {
  const { action, data } = res
  console.log(action, data)

  if (action === ACTION.CHECKSTATE) {
    checkInState.value = data.checkInState === '2' ? '' : data.checkInState
    paging.value.reload()
  }
  if (action === ACTION.IMGPREVIEW) {
    previewImgs.value = data.imgs
    imgIndex.value = data.index
    previewImgShow.value = true
  }
  if (action === ACTION.CHECKTABLE) {
    checkTableId.value = data.id
    checkTableShow.value = true
  }
}

onLoad((params) => {
  routerParams = params
})
onMounted(() => {
  paging.value.reload()
})

// 监听详情数据变化，当详情数据更新时刷新列表
watch(
  () => DetailSerivce.detailInfo.value,
  (newVal) => {
    if (newVal && paging.value) {
      paging.value.reload()
    }
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
.container {
  padding: 0 30rpx;
}

// .list-item {
//   width: 100%;
//   background-color: #fff;
//   margin-bottom: 20rpx;
//   font-size: 28rpx;
//   color: #333;
//   padding: 16rpx;
// }
</style>
