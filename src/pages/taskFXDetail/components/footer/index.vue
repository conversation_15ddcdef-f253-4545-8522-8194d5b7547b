<template>
  <view class="footer-box">
    <!-- 开始任务 现场打卡 结束任务 -->
    <wd-button
      v-if="showXcdk"
      style="min-width: 0; border-radius: 4px"
      size="medium"
      @click="clockInHandle"
    >
      现场打卡
    </wd-button>
    <template v-else>
      <!-- 根据用户权限设置显示功能按钮 -->
      <wd-button type="info" size="large" @click="doEndTask" :loading="endLoading" block>
        结束任务
      </wd-button>
      <wd-button type="primary" @click="scanCode" size="large" block>扫码检查</wd-button>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { DetailSerivce } from '../../detailService'
import { PostcompletionTaskAPI } from '@/pages/task/featch'
import { useUserStore, useAppStore, useAppReceiveStore } from '@/store'
import { ACTION } from '../../constant'
import { wychScan } from '../../../task/scanService'
import WsSocket from '@/utils/socket'
import { useMessage } from 'wot-design-uni'
import { checkPointExist } from '../../fetchData'

defineOptions({ name: 'TaskFXDetailFooter' })

const message = useMessage()
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
interface IParams {
  taskId: string
  planId: string
  tabindex: string
}
interface IProps {
  showXcdk: boolean
  data: IParams
}

const props = withDefaults(defineProps<IProps>(), {
  showXcdk: null,
  data: () => ({
    taskId: '',
    planId: '',
    tabindex: '',
  }),
})

const emits = defineEmits(['action'])

// 重新打卡/现场打卡
const info = DetailSerivce.detailInfo.value
function clockInHandle() {
  uni.navigateTo({
    url: `/pages/task/clockIn?taskId=${info.taskId}&lat1=${info.unitPointY}&lon1=${info.unitPointX}&range=${info.printRange}`,
  })
}

/* 结束任务操作 */
const endLoading = ref(false)
function doEndTask() {
  uni.showModal({
    title: '提示',
    content: '确认要结束任务吗?结束任务后会上报检查出的隐患',
    success: async function (res) {
      if (res.confirm) {
        endLoading.value = true
        try {
          const info = DetailSerivce.detailInfo.value
          const res: any = await PostcompletionTaskAPI(info.taskId, userInfo.id)
          endLoading.value = false
          if (res.code === 'success') {
            uni.showToast({
              icon: 'none',
              title: res.message,
            })
            emits('action', {
              action: ACTION.RELOADDETAIL,
              data: null,
            })
          } else {
            uni.showModal({
              title: '提示',
              content: res.message,
              showCancel: false, // 是否显示取消按钮
              confirmText: '我知道了', // 确认按钮的文字，默认为"确定"
              success: function (res) {
                if (res.confirm) {
                  uni.navigateBack()
                  // 确定按钮的逻辑
                }
              },
            })
          }
        } catch (error) {
          endLoading.value = false
        }
      } else if (res.cancel) {
        // 在这里执行取消后的操作
        endLoading.value = false
      }
    },
  })
}

// ============扫码===开始=================
const currentEnvInfo = useAppStore().appEnv

function scanCode() {
  if (currentEnvInfo.wychApp) {
    return wychScan.openSacn(props.data.taskId)
  }
  webUni.getEnv((env: any) => {
    if (env.miniprogram) {
      webUni.navigateTo({
        url: `/pages/common/scanPage?scanOri=${JSON.stringify({ taskId: props.data.taskId, isCheck: true })}`,
      })
    } else {
      webUni.postMessage({
        data: {
          action: 'scan',
          type: '',
          res: { taskId: props.data.taskId, isCheck: true },
        },
      })
    }
  })
}

const receiveStore = useAppReceiveStore()
// 解析扫码信息并获取pointType和pointId
function parseQRCodeInfo(paramsObj: any) {
  let pointType = '1'
  let devicePointId = ''
  if (paramsObj.pointId && paramsObj.type === 'risk') {
    // pointid=xxx&type=risk
    pointType = '2'
    devicePointId = paramsObj.pointId
  } else if (paramsObj.pointId && !paramsObj.type) {
    // pointid=xxx
    pointType = '1'
    devicePointId = paramsObj.pointId
  } else if (paramsObj.taskDeviceId) {
    // taskDeviceId=xxx
    pointType = '3'
    devicePointId = paramsObj.taskDeviceId
  } else if (paramsObj.keyPartId) {
    // keyPartId=xxx
    pointType = '4'
    devicePointId = paramsObj.keyPartId
  }

  return { pointType, devicePointId }
}

// 验证点位是否存在的函数
async function validatePointId(paramsObj: any): Promise<boolean> {
  try {
    const { pointType, devicePointId } = parseQRCodeInfo(paramsObj)
    const res = await checkPointExist({
      taskId: props.data.taskId,
      pointType,
      devicePointId,
    })
    return res.code === 'success' && res.data === true
  } catch (error) {
    console.error('验证点位失败:', error)
    return false
  }
}
watch(
  () => receiveStore.appData,
  async (val) => {
    if (val && val.action !== 'shellScan') return
    console.log('隐患=>点位巡查接收扫码数据:::', JSON.parse(val.data))
    const { paramsObj } = JSON.parse(val.data)
    const isValid = await validatePointId(paramsObj)
    console.log('isValid', isValid)
    if (!isValid) return scanPrompt()
    const queryString = Object.keys(paramsObj)
      .map((key) => `${key}=${paramsObj[key]}`)
      .join('&')
    uni.navigateTo({
      url: `/pages/task/scanCodes?taskId=${props.data.taskId}&qrCodeValue=${encodeURIComponent(queryString)}`,
    })
  },
)

const $WS = new WsSocket({ topic: '/topic/scanEvent' })

// 监听socket消息
$WS.onMessage = async (msg) => {
  console.log('子应用 socket消息:::', msg)
  const { action, data } = msg
  if (action && action !== 'shellScan') return
  console.log('设备检查接收扫码数据:::', data)
  const { paramsObj } = JSON.parse(data)

  const isValid = await validatePointId(paramsObj)
  if (!isValid) return scanPrompt()

  const queryString = Object.keys(paramsObj)
    .map((key) => `${key}=${paramsObj[key]}`)
    .join('&')

  uni.navigateTo({
    url: `/pages/task/scanCodes?taskId=${props.data.taskId}&qrCodeValue=${encodeURIComponent(queryString)}`,
  })
}

function scanPrompt() {
  uni.showModal({
    title: '请输入巡查点位ID',
    content: '',
    editable: true,
    success: async function (res) {
      if (res.confirm) {
        // 手动输入时使用 pointType = 1
        const inputParamsObj = { pointId: res.content, type: 'risk' }
        const isValid = await validatePointId(inputParamsObj)
        if (isValid) {
          return uni.navigateTo({
            url: `/pages/task/scanCodes?taskId=${props.data.taskId}&qrCodeValue=${encodeURIComponent('pointId=' + res.content)}`,
          })
        } else {
          // 输入错误
          uni.showToast({ title: '巡查点位ID输入错误！', icon: 'none', duration: 1500 })
          setTimeout(() => scanPrompt(), 1500)
        }
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    },
  })
}

onBeforeUnmount(() => {
  $WS.destroy()
})
// ============扫码===结束=================
</script>

<style scoped lang="scss">
.footer-box {
  @apply w-full flex flex-row flex-nowrap items-center justify-between;
  gap: 20rpx;
  padding: 30rpx 20rpx;
}
</style>
