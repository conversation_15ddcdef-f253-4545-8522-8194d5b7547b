import { $api } from '@/api/index'
import { http } from '@/utils/http'
import { IObj, IPageRes } from '@/types/custom'
import {
  IChartsData,
  IHazardPage,
  IHazardTaskDetailsData,
  IStatistics,
  IPointItem,
  ICheckTable,
} from './type'

// 获取任务详情
export const getTaskDetail = (id: string) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.getHazardPlanDetai, { taskId: id })
  return http.post<IHazardTaskDetailsData>(url)
}

// app任务隐患统计
export const getTaskStatistics = (query: any) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.appTaskstatistics, { ...query })
  return http.get<IChartsData>(url)
}

// 获取等级列表
export const getHazardGrade = (data: any) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.getHazardGrade)
  return http.post<any>(url, { ...data })
}

// 获取所有的下属单位
export const getUnits = (data: any) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.getAllUnit)
  return http.post(url, { ...data })
}

// 隐患列表 businessId flag
export const pageEvent = (data: any) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.pageEvent)
  return http.post<IHazardPage>(url, { ...data })
}

// 获取风险点数量统计
export const checkPointBars = (id: string) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.getPointStatistics, { taskId: id })
  return http.post<IStatistics>(url)
}

// 获取检车点列表
export const pageCheckPointList = (params: IObj<any>) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.pageCheckPointList)
  return http.post<IPageRes<IPointItem>>(url, params)
}

// 获取检查表
export const getCheckTableDetail = (id: string) => {
  const url = $api.getUrl($api.type.hazard, $api.name.hazard.getCheckTableDetail, { id })
  return http.post<ICheckTable>(url)
}

// 判断点位是否存在
export const checkPointExist = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardTask/checkPointIn', Parameters)
}
