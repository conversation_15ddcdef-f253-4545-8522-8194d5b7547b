<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>

<template>
  <view class="taskFx-detail-page">
    <SafetyNavbar title="任务详情"></SafetyNavbar>
    <CustomTabs
      class="w-full"
      :tabs="tabs"
      :activeIndex="activeIndex"
      @handleClick="handleChange"
    ></CustomTabs>
    <view class="page-main">
      <keep-alive>
        <component :is="curComp" />
      </keep-alive>
    </view>
    <view class="page-bottom" v-if="showBottom">
      <FooterComp :show-xcdk="showXCDK" @action="actionHandle" :data="routerParams" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import CheckDetail from './components/checkDetail/index.vue'
import HidenList from './components/hidenList/index.vue'
import FooterComp from './components/footer/index.vue'
import { DetailSerivce } from './detailService'
import { getTaskDetail } from './fetchData'
import { PoststartTaskAPI, getTaskClockInOne } from '@/pages/task/featch'
import { useUserStore } from '@/store'
import { Nullable } from '@/types/custom'
import { ACTION } from './constant'

defineOptions({ name: 'TaskFXDetail' })

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}

interface IParams {
  taskId: string
  planId: string
  tabindex: string
}
const routerParams = ref<Nullable<IParams>>(null)
let fzrlist = []
let cyrsIds = []

const showBottom = computed(() => {
  const includesUser = fzrlist.includes(userInfo.id) || cyrsIds.includes(userInfo.id)
  return (
    +routerParams.value?.tabindex === 0 &&
    +DetailSerivce.detailInfo.value?.taskState === 2 &&
    +DetailSerivce.detailInfo.value?.checkEndOperate === 2 &&
    includesUser
  )
})

const showXCDK = computed(() => {
  return (
    +DetailSerivce.detailInfo.value?.isNeedClock === 1 &&
    !DetailSerivce.detailInfo.value?.isClockInOne
  )
})

const tabs = ['检查详情', '隐患清单']
const activeIndex = ref(0)

const curComp = computed(() => {
  return activeIndex.value === 0 ? markRaw(CheckDetail) : markRaw(HidenList)
})

function handleChange(value) {
  activeIndex.value = value
}
const unitList = ref([])
async function getDetailData() {
  const { code, data } = await getTaskDetail(routerParams.value.taskId)
  if (code === 'success') {
    fzrlist = data.fzrsIds.split(',')
    cyrsIds = data.cyrsIds.split(',')
    DetailSerivce.detailInfo.value = data
    if (+data.taskState === 1) {
      // 更改任务状态
      if (fzrlist.includes(userInfo.id) || cyrsIds.includes(userInfo.id)) {
        const res = await doStartTask()
        if (res.code === 'success') {
          getDetailData()
        }
      }
    } else {
      // 是否需要现场打卡
      if (+data.isNeedClock === 1) {
        const { isClock } = await getTaskClockInOneAPI({
          taskId: routerParams.value.taskId,
          userId: userInfo.id,
        })
        if (isClock) {
          DetailSerivce.detailInfo.value.isClockInOne = isClock
        }
      }
    }

    // 获取当下任务的检查对象
    unitList.value = data.unitNames.split(',').map((item, index) => {
      return {
        unitName: item,
        id: data.unitIds.split(',')[index],
      }
    })
    uni.setStorageSync('unitList', unitList.value)
  }
}
// 开始任务
async function doStartTask() {
  return new Promise<any>((resolve, reject) => {
    try {
      PoststartTaskAPI(routerParams.value.taskId, userInfo.id).then((res) => {
        resolve(res)
      }, reject)
    } catch (error) {
      reject(error)
    }
  })
}
// 请求最近一条打卡记录, 判断是否打卡
function getTaskClockInOneAPI(params) {
  try {
    return new Promise<any>((resolve, reject) => {
      getTaskClockInOne(params)
        .then((res: any) => {
          if (res.data?.id) {
            resolve({
              isClock: true,
            })
          } else {
            resolve({
              isClock: false,
            })
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

const actionHandle = (res: { action: string; data: any }) => {
  const { action, data } = res
  if (action === ACTION.RELOADDETAIL) {
    getDetailData()
  }
}

onLoad(async (params: IParams) => {
  routerParams.value = params
  try {
    await getDetailData()
  } catch (error) {}
})

onShow(() => {
  // 页面显示时刷新数据，确保从扫码页面返回后数据是最新的
  if (routerParams.value) {
    getDetailData()
  }
})
</script>

<style scoped lang="scss">
.taskFx-detail-page {
  @apply w-full h-full flex flex-col items-start gap-0;

  .page-main {
    @apply w-full min-h-0 flex-1 overflow-hidden;
    background: #f9f9f9;
  }

  .page-bottom {
    @apply w-full;
    border-top: 1px solid #eee;
  }
}
</style>
