<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '扫码检查',
  },
}
</route>
<template>
  <SafetyNavbar :title="info.qrCodeName"></SafetyNavbar>
  <view class="container" v-if="!loading">
    <view
      v-if="isinternal"
      style="display: flex; flex-direction: column; align-items: center; text-align: center"
    >
      <img
        style="width: 62%"
        src="../../static/pages/images/file-read-33677.png"
        alt=""
        srcset=""
      />
      <div style="width: 80%; margin: auto">未能识别当前二维码</div>
    </view>
    <view v-else class="content-wrapper">
      <view class="batch-operation-container">
        <view class="batch-normal-btn" @click="batchSetAllNormal">批量正常</view>
      </view>
      <!-- 上传图片 -->
      <wd-cell title="排查过程照片：" />
      <uploadButton
        ref="uploadButtonRef"
        :imginfo="imgFiles"
        :imgslen="5"
        @getFileObjList="getFilelist"
      ></uploadButton>

      <view
        v-if="!Object.keys(list).length"
        style="display: flex; flex-direction: column; align-items: center; text-align: center"
      >
        <img
          style="width: 62%"
          src="../../static/pages/images/file-read-33677.png"
          alt=""
          srcset=""
        />
        <div style="width: 80%; margin: auto">
          您正在查看的点位与当前任务不匹配哦，请检查并前往正确的任务点位
        </div>
      </view>
      <view v-else>
        <z-paging
          ref="paging"
          v-model="displayCheckItems"
          @query="loadCheckItems"
          :default-page-size="45"
          :auto="true"
          class="check-items-paging"
          use-virtual-list
          :force-close-inner-list="true"
        >
          <view
            v-for="item in displayCheckItems"
            :key="item.uniqueId"
            :id="`zp-id-${item.uniqueId}`"
          >
            <view v-if="item.type === 'parent'" class="parent-header">
              <view class="header">
                <view class="header-left">
                  <wd-img class="header-title-icon" :src="dangerIcon"></wd-img>
                  <text style="font-size: 16px; color: #333333; font-weight: 500">
                    检查类别{{ item.parentIndex + 1 }}：{{ item.checkContent }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 子级检查项 -->
            <view v-else-if="item.type === 'child'" class="child-item">
              <view class="child-item-content">
                检查内容{{ item.childIndex + 1 }}：{{ item.checkDetail }}
              </view>
              <view class="child-item-buttons">
                <label class="radio">
                  <wd-icon
                    v-if="item.checkState === '1'"
                    name="check-circle-filled"
                    size="26px"
                    style="color: #0256ff"
                    @click="change(item)"
                  ></wd-icon>
                  <wd-icon v-else name="check-circle" size="26px" @click="change(item)"></wd-icon>
                </label>
                <label class="radio" v-if="!item.checkState || item.checkState === '1'">
                  <wd-icon name="close-circle" size="26px" @click="changeBad(item)"></wd-icon>
                </label>
                <wd-icon
                  v-if="item.checkState === '2'"
                  style="color: #d42121"
                  name="close-circle-filled"
                  size="26px"
                  @click="goForward(item)"
                ></wd-icon>
              </view>
            </view>
          </view>
        </z-paging>
      </view>
    </view>
    <view v-if="list && list.checkAreaVos" class="list-button">
      <view class="list-button-cancel" @click="cancel">返回</view>
      <view class="list-button-sure" @click="submitHandle">提交</view>
    </view>
  </view>

  <!-- 隐患弹框 -->
  <wd-popup v-model="show" position="bottom" closable>
    <view class="v-popup">
      <view class="v-popup-title">常见隐患</view>
      <view
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-right: 5px;
          border-bottom: 0.01rem solid #ebeef5;
        "
        v-for="(item, index) in troubleInfo"
        :key="item"
      >
        <view class="v-popup-content">
          <text style="width: 10px; font-size: 14px; color: #333333">{{ index + 1 }}.</text>
          <view style="text-align: left; margin-left: 5px; font-size: 14px; color: #333333">
            {{ item.essentialFactorDescribe }}
          </view>
        </view>
        <wd-button
          style="
            min-width: 80px;
            margin-left: auto;
            color: #0256ff;
            background: #e5eeff;
            border-radius: 88px 88px 88px 88px;
            border: 1px solid #0256ff;
          "
          @click="goReport(item.id, item.zid)"
          size="medium"
        >
          上报
        </wd-button>
      </view>
      <view class="v-popup-btn">
        <view class="cancel-btn" @click="show = false" size="medium">取消</view>
        <view class="report-btn" @click="goReport('')" size="medium">上报其他隐患</view>
      </view>
    </view>
  </wd-popup>

  <!-- 状态切换确认提示框 -->
  <wd-popup v-model="showStatusConfirm" position="center">
    <view class="status-confirm-popup">
      <view class="status-confirm-content">
        <view>重新上报隐患?</view>
        <view>重新上报后将会清除已上报的隐患信息</view>
      </view>
      <view class="status-confirm-buttons">
        <wd-button class="status-confirm-cancel" @click="handleStatusCancel" size="medium">
          取消
        </wd-button>
        <wd-button class="status-confirm-confirm" @click="handleStatusConfirm" size="medium">
          确定
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { nextTick } from 'vue'
import { useAppStore, useUserStore } from '@/store'
import {
  selectPointDetails,
  updateDayTaskPointContentList,
  postDetailByQrCodeValueAPI,
  inspectDetail,
} from '@/pages/task/featch'
import { TaskCheckPointVo } from '@/pages/task/type'
import SafetyNavbar from '@/pages/components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import dangerIcon from '@/pages/taskFXDetail/assets/dangerIcon.png'

const { appEnv, appInfo } = useAppStore()
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const type = ref<string>('')
const taskId = ref<string>('')
const pointId = ref<string>('')
const isinternal = ref<boolean>(false)
const info = ref<any>({})

const loading = ref(true)
const show = ref<boolean>(false)
const troubleInfo = ref<any>([])
const currentCheckItem = ref<any>(null)
const collapseValue = ref<string[]>([])

// 虚拟列表相关变量
const paging = ref()
const allCheckItems = ref<any[]>([]) // 所有扁平化的检查项
const displayCheckItems = ref<any[]>([]) // 当前显示的检查项
const firstLoading = ref(true)

// 状态切换确认提示框相关变量
const showStatusConfirm = ref<boolean>(false)
const statusConfirmData = ref<any>({
  item: null,
  newState: '',
  oldState: '',
  action: '', // 'error-to-error' 或 'error-to-correct'
})

onLoad((params: any) => {
  uni.showLoading({
    title: '加载中...',
    icon: 'none',
    mask: true,
  })
  // console.log('获取参数', params)
  // params.qrCodeValue = 'pointId=07001000001'
  if (
    params.qrCodeValue.includes('pointId') ||
    params.qrCodeValue.includes('keyPartId') ||
    params.qrCodeValue.includes('taskDeviceId')
  ) {
    params.qrCodeValue = encodeURIComponent(params.qrCodeValue)
    // params.taskId = '264a10b5430c4bdbb234c314f39be09c'
    taskId.value = params.taskId
    // 根据扫码出来的内容去置换pointId从而更改检查状态
    getDetailByQrCodeValue(params)
  } else {
    console.log('非园区内二维码')
    uni.hideLoading()
    isinternal.value = true
    info.value.qrCodeName = '点位详情'
    loading.value = false
  }
})

onShow(() => {
  // 检查隐患上报成功标识
  checkHazardReportSuccess()
})
const zPid = ref<string>('')
// 根据扫码出来的内容去置换pointId从而更改检查状态
function getDetailByQrCodeValue(params: any) {
  postDetailByQrCodeValueAPI(params)
    .then((res: any) => {
      if (res.data.code === 'error') {
        list.value = {}
      } else {
        info.value = res.data
        pointId.value = res.data.pointId
        zPid.value = res.data.id
        // 获取点位信息
        getPointList()
      }
    })
    .catch(() => {
      // console.log(err, '这里是扫码失败')
      list.value = {}
      uni.hideLoading()
      loading.value = false
    })
}
const imgFiles = ref<any>([])
// 获取图片列表
function getFilelist(event: any) {
  console.log('event', event)
  imgFiles.value = event
}

const list = ref<TaskCheckPointVo>({})

// 数据扁平化函数
const flattenCheckData = () => {
  if (!list.value.checkAreaVos) return []

  const flattened: any[] = []

  list.value.checkAreaVos.forEach((parentItem, parentIndex) => {
    // 添加父级标题
    flattened.push({
      type: 'parent',
      uniqueId: `parent-${parentIndex}`,
      parentIndex,
      checkContent: parentItem.checkContent,
      originalParentItem: parentItem,
    })

    // 添加子级检查项
    if (parentItem.childCheckAreaList) {
      parentItem.childCheckAreaList.forEach((childItem, childIndex) => {
        flattened.push({
          type: 'child',
          uniqueId: `child-${parentIndex}-${childIndex}`,
          parentIndex,
          childIndex,
          ...childItem,
          originalChildItem: childItem,
          originalParentItem: parentItem,
        })
      })
    }
  })

  return flattened
}

// 分页加载函数
const loadCheckItems = (pageNo: number, pageSize: number) => {
  console.log('loadCheckItems', pageNo, pageSize)
  if (pageNo === 1) {
    // 第一页，初始化所有数据
    allCheckItems.value = flattenCheckData()
  }

  const startIndex = (pageNo - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = allCheckItems.value.slice(startIndex, endIndex)

  setTimeout(() => {
    paging.value?.completeByTotal(pageData, allCheckItems.value.length)
  }, 100)
}

const change = (e: any) => {
  // 检查当前状态，如果是错误状态切换到正确状态，需要提示
  if (e.checkState === '2') {
    // 错误状态切换至正确状态，显示确认提示框
    statusConfirmData.value = {
      item: e,
      newState: '1',
      oldState: '2',
      action: 'error-to-correct',
    }
    showStatusConfirm.value = true
  } else {
    // 直接切换到正常状态，不调用接口
    e.checkState = '1'
    // 同时更新原始数据
    if (e.originalChildItem) {
      e.originalChildItem.checkState = '1'
    }
  }
}
const changeBad = (e: any) => {
  // 点击错误状态时，不选中，直接显示隐患弹框
  // 保存当前检查项信息，用于后续隐患上报
  currentCheckItem.value = e.originalChildItem || e
  getDetail(e.id)
}

const goForward = (e: any) => {
  // 错误状态下再次点击错误，显示确认提示框
  statusConfirmData.value = {
    item: e,
    newState: '2',
    oldState: '2',
    action: 'error-to-error',
  }
  showStatusConfirm.value = true
}

// 批量设置所有未选择状态的检查项为正常
function batchSetAllNormal(event?: any) {
  event?.stopPropagation()

  uni.showLoading({
    title: '处理中...',
    mask: true,
  })

  setTimeout(async () => {
    try {
      // 收集所有未设置状态的子项
      const itemsToProcess: any[] = []

      // 遍历所有父项和子项
      list.value.checkAreaVos?.forEach((parentItem) => {
        if (parentItem.childCheckAreaList) {
          parentItem.childCheckAreaList.forEach((childItem) => {
            if (!childItem.checkState) {
              itemsToProcess.push(childItem)
            }
          })
        }
      })

      if (itemsToProcess.length === 0) {
        uni.hideLoading()
        return
      }

      // 分批处理项目
      let processedCount = 0
      const batchSize = 200

      while (processedCount < itemsToProcess.length) {
        const endIndex = Math.min(processedCount + batchSize, itemsToProcess.length)
        for (let i = processedCount; i < endIndex; i++) {
          itemsToProcess[i].checkState = '1'
        }
        processedCount = endIndex
        if (processedCount < itemsToProcess.length) {
          await new Promise((resolve) => setTimeout(resolve, 0))
        }
      }

      // 重新加载虚拟列表数据
      allCheckItems.value = flattenCheckData()
      paging.value?.reload()

      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '所有内容已设置正常',
        duration: 1000,
      })
    } catch (error) {
      uni.hideLoading()
      console.error('批量设置正常状态失败:', error)
    }
  }, 50)
}
function cancel() {
  uni.navigateBack()
}

const submitHandle = () => {
  // 检查所有子项是否都已完成
  for (let i = 0; i < list.value.checkAreaVos.length; i++) {
    const parentItem = list.value.checkAreaVos[i]
    if (parentItem.childCheckAreaList) {
      for (let j = 0; j < parentItem.childCheckAreaList.length; j++) {
        const childItem = parentItem.childCheckAreaList[j]
        if (!childItem.checkState) {
          uni.showToast({
            icon: 'none',
            title: '还有未完成的检查项',
          })
          return
        }
      }
    }
  }
  list.value.fileUploadVos = imgFiles.value
  updateDayTaskPointContentList(list.value).then((res) => {
    if ((res.code as any) === 'success') {
      uni.showToast({
        icon: 'none',
        title: '检查成功',
      })
      cancel()
    }
  })
}
const getPointList = () => {
  selectPointDetails({
    taskId: taskId.value,
    pointId: pointId.value,
  })
    .then((res: any) => {
      console.log(res, '====点位详情====')
      if (res.data) {
        list.value = res.data
        imgFiles.value = list.value.fileUploadVos
        collapseValue.value = res.data.checkAreaVos.map((item: any) => item.checkContent)

        // 初始化虚拟列表数据
        firstLoading.value = false
        nextTick(() => {
          if (paging.value && list.value.checkAreaVos?.length) {
            paging.value.reload()
          }
        })
      } else {
        list.value = {}
        firstLoading.value = false
      }
      uni.hideLoading()
    })
    .catch(() => {
      uni.hideLoading()
      firstLoading.value = false
    })
    .finally(() => (loading.value = false))
}

// 检查隐患上报成功标识
function checkHazardReportSuccess() {
  const hazardReportResult = uni.getStorageSync('hazardReportSuccess')

  if (hazardReportResult && hazardReportResult.success) {
    console.log('检测到隐患上报成功:', hazardReportResult)
    const { checkContentId } = hazardReportResult
    // 在嵌套结构中查找并更新子项状态为错误
    for (let i = 0; i < list.value.checkAreaVos?.length; i++) {
      const parentItem = list.value.checkAreaVos[i]
      if (parentItem.childCheckAreaList) {
        const childIndex = parentItem.childCheckAreaList.findIndex(
          (child) => child.id === checkContentId,
        )
        if (childIndex !== -1) {
          list.value.checkAreaVos[i].childCheckAreaList[childIndex].checkState = '2'
          allCheckItems.value = flattenCheckData()
          if (paging.value) {
            paging.value.reload()
          }
          break
        }
      }
    }

    // 清除标识，避免重复处理
    uni.removeStorageSync('hazardReportSuccess')
  }
}

// 获取常见隐患项
function getDetail(id: any) {
  inspectDetail(id, '').then((res: any) => {
    console.log('hazardCheckArea/detail 返回的数据:', res)
    const d = res.data
    if (d.essentialFactorList.length) {
      // 显示隐患弹框
      show.value = true
      troubleInfo.value = d.essentialFactorList.map((item: any) => {
        return {
          ...item,
          zid: d.id,
        }
      })
    } else {
      show.value = false
      goReport('')
    }
  })
}

// 隐患上报
function goReport(id: any, zid = '') {
  let url: string
  // 非快捷上报
  if (!id) {
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      currentCheckItem.value.id +
      '&taskId=' +
      taskId.value +
      '&pointId=' +
      zPid.value +
      '&types=0'
  } else {
    // 快捷上报
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      currentCheckItem.value.id +
      '&FactorId=' +
      id +
      '&checkId=' +
      zid +
      '&taskId=' +
      taskId.value +
      '&pointId=' +
      zPid.value +
      '&types=0'
  }

  show.value = false
  uni.navigateTo({
    url,
  })
}

// 状态切换确认处理函数
function handleStatusConfirm() {
  const { item, newState, action } = statusConfirmData.value
  item.checkState = newState

  // 同时更新原始数据
  if (item.originalChildItem) {
    item.originalChildItem.checkState = newState
  }

  if (action === 'error-to-error') {
    // 错误状态下再次点击错误，继续走隐患上报流程
    currentCheckItem.value = item.originalChildItem || item
    getDetail(item.id)
  }

  // 同步更新虚拟列表数据
  allCheckItems.value = flattenCheckData()
  if (paging.value) {
    paging.value.reload()
  }

  showStatusConfirm.value = false
}

function handleStatusCancel() {
  // 取消操作，恢复之前的状态
  showStatusConfirm.value = false
}
</script>
<style lang="scss" scoped>
::v-deep {
  .wd-navbar__left--hover,
  .wd-navbar__right--hover {
    background: none !important;
  }

  .wd-icon-arrow-left {
    color: white;
  }

  .wd-navbar__title {
    font-size: 1.25rem;
    font-weight: 500;
    color: white;
  }
}

.list-head {
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: 88px;
  background-image: url('@/static/safety/image/work_bg.png');
  background-size: 100%, 100%;

  .list-head-title {
    width: 100%;
    font-size: 20px;
    line-height: 44px;
    color: #fff;
    text-align: center;
  }
}

.list-item {
  display: flex;
  justify-content: space-between;
  width: 96vw;
  margin: 15px auto;

  .list-item-context {
    width: calc(100% - 100px);
    font-size: 14px;
    color: rgb(75, 75, 75);
  }

  .list-item-button {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 80px;
  }
}

.child-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  padding: 0 40rpx;

  &:first-child {
    margin-top: 0;
  }

  .child-item-content {
    flex: 1;
    font-size: 14px;
    color: #333;
  }

  .child-item-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 16px;

    .radio {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px;
  overflow: hidden;
  position: relative;
}

.scroll-content {
  padding-bottom: 5rem;
}

.check-items-paging {
  flex: 1;
  height: calc(100vh - 20rem);
  min-height: 20rem;
  position: relative;
  z-index: 1;
  margin-top: 10px;
}

.check-items-paging :deep(.z-paging-content) {
  position: relative !important;
}

.check-items-paging :deep(.z-paging-content-fixed) {
  position: relative !important;
  top: auto !important;
  left: auto !important;
}

.parent-header {
  background-color: #f8f9fa;
  padding: 15rpx 20rpx;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 10rpx;
}

.batch-operation-container {
  position: fixed;
  top: 165rpx;
  right: 50rpx;
  display: flex;
  justify-content: flex-end;
  z-index: 10;
}

.batch-normal-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  font-size: 12px;
  color: #0256ff;
  background-color: #e5eeff;
  border: 1px solid #0256ff;
  border-radius: 16px;
}

.batch-normal-btn:active {
  opacity: 0.8;
}

.list-button {
  position: fixed;
  bottom: 0px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 80px;
  background-color: #fff;
  border-top: 1px solid #f2f2f2;

  .list-button-cancel {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 120px;
    height: 40px;
    color: #666;
    border: 1px solid #666;
    border-radius: 20px;
  }

  .list-button-sure {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 120px;
    height: 40px;
    color: #fff;
    background-color: #3a86f6;
    border-radius: 18px;
  }
}

.v-popup {
  text-align: center;

  .v-popup-title {
    padding: 1rem;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 70%;
    padding: 1rem 1.5rem;
  }

  .v-popup-btn {
    display: flex;
    justify-content: space-between;
    height: 100rpx;

    .cancel-btn {
      flex: 1;
      color: #969696;
      line-height: 100rpx;
    }

    .report-btn {
      flex: 1;
      color: #fff;
      line-height: 100rpx;
      background: #0256ff;
    }
  }
}

// 状态切换确认提示框样式
.status-confirm-popup {
  width: 300px;
  background: #fff;
  border-radius: 12px;
  text-align: center;

  .status-confirm-content {
    padding: 20px;
    font-size: 14px;
    color: #666;
    text-align: center;
  }

  .status-confirm-buttons {
    display: flex;
    border-top: 1px solid #f0f0f0;

    .status-confirm-cancel,
    .status-confirm-confirm {
      flex: 1;
      border-radius: 0;
      border: none;
      padding: 20px;
      font-size: 16px;
    }

    .status-confirm-cancel {
      color: #666;
      background: #fff;
    }

    .status-confirm-confirm {
      color: #fff;
      background: #0256ff;
    }
  }
}
.header {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 10rpx;
  font-size: 32rpx;
  line-height: 1.5em;
  color: #333;
  font-weight: 700;

  .header-left {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 10rpx;
  }

  .header-right {
    flex-shrink: 0;
    margin-right: 15rpx;
  }
}
.header-title-icon {
  width: 32rpx;
  height: 32rpx;
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }
}
</style>
