<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>

<template>
  <SafetyNavbar title="任务详情"></SafetyNavbar>
  <view class="container" style="overflow: auto">
    <wd-collapse v-model="collapsevauel">
      <wd-collapse-item title="检查人员信息" name="itme1">
        <view class="contentitme">检查负责人：{{ info.fzrs }}</view>
        <view class="contentitme">检查参与人：{{ info.cyrs }}</view>
      </wd-collapse-item>
    </wd-collapse>

    <wd-collapse v-model="collapsevauel">
      <wd-collapse-item title="巡查点位" name="itme">
        <view
          class="itme1 j-spacebetween"
          @click="goCheckTable($i.checkTableId)"
          v-for="($i, index) in planCheckPointList"
          :key="index"
        >
          <view class="cfontsize" style="width: 5%">{{ index + 1 }}</view>
          <view class="cfontsize ellipsis1">{{ $i.pointFullName || '--' }}</view>
          <view class="cfontsize sizeColor ellipsis1">
            {{ $i.checkTableName || '--' }}
          </view>
          <view class="cfontsize" style="width: 20%" v-if="$i.checkTableContentNum != 0">
            {{ $i.checkTableContentNum }}项目>
          </view>
          <view class="cfontsize" v-else>{{ defaultNum }}默认></view>
        </view>
        <view
          style="margin: 1rem 0 0.5rem 35%"
          @click="geloadMore()"
          v-if="totalNum >= 3 && !totalSHOW"
        >
          查看全部(共{{ totalNum }}条)
          <wd-icon name="arrow-down" size="14px"></wd-icon>
        </view>
      </wd-collapse-item>
    </wd-collapse>

    <view class="statistics">
      <view :class="[activeNum === 0 ? 'active' : '', 'word-box']" @click="filterList(0)">
        <view class="num-css">{{ devieStatisticsinfo.deviceTotalNum }}</view>
        <view v-if="info.checkRange == 2">全部设备</view>
        <view v-if="info.checkRange == 3">全部点位</view>
      </view>
      <view
        style="height: 100%"
        :class="[activeNum === 1 ? 'active' : '', 'word-box']"
        @click="filterList(1)"
      >
        <view class="num-css">{{ devieStatisticsinfo.checkedNum }}</view>
        <view>已巡查</view>
      </view>
      <view
        style="height: 100%"
        :class="[activeNum === 2 ? 'active' : '', 'word-box']"
        @click="filterList(2)"
      >
        <view class="num-css">{{ devieStatisticsinfo.uncheckedNum }}</view>
        <view>待巡查</view>
      </view>
      <view
        style="height: 100%"
        :class="[activeNum === 3 ? 'active' : '', 'word-box']"
        @click="filterList(3)"
      >
        <view class="num-css" style="color: #00a720">{{ devieStatisticsinfo.normaNum }}</view>
        <view>正常</view>
      </view>
      <view
        style="height: 100%"
        :class="[activeNum === 4 ? 'active' : '', 'word-box']"
        @click="filterList(4)"
      >
        <view class="num-css" style="color: #f02626">{{ devieStatisticsinfo.abnormaNum }}</view>
        <view>异常</view>
      </view>
    </view>

    <view v-if="info.checkRange === '3'" style="padding: 0 16px">
      <view style="height: 100%" v-if="pointList.length > 0">
        <view
          v-for="(item, index) in pointList"
          :key="index"
          class="info-first"
          @click="getPointfool(item, index)"
        >
          <view class="info-first-title">
            <view class="info-first-name" style="font-size: 18px; font-weight: 700">
              {{ item.buildingName }}
            </view>
            <view class="info-first-icon">
              <wd-icon
                name="arrow-down"
                size="22px"
                style="transition: all 0.3s"
                :style="{ transform: item.open ? 'rotate(-180deg)' : 'rotate(0)' }"
              ></wd-icon>
            </view>
          </view>
          <view class="info-first-context" v-if="item.open">
            <view
              class="info-first"
              v-for="(lc, lcindex) in item.lclist"
              :key="lcindex"
              @click.stop="getPointshebei(lc, lcindex, index)"
            >
              <view class="info-first-title">
                <view class="info-first-name">{{ lc.floorName }}</view>
                <view class="info-first-icon">
                  <wd-icon
                    name="arrow-down"
                    size="22px"
                    style="transition: all 0.3s"
                    :style="{ transform: lc.open ? 'rotate(-180deg)' : 'rotate(0)' }"
                  ></wd-icon>
                </view>
              </view>
              <view class="info-first-context" v-if="lc.open">
                <view
                  class="info-first"
                  v-for="(s, sindex) in lc.shebeilist"
                  :key="sindex"
                  @click.stop="getjcx(s.deviceId, index, lcindex, sindex, s)"
                >
                  <view class="info-first-title">
                    <view
                      class="info-first-name"
                      style="width: 100%; display: flex; justify-content: space-around"
                    >
                      <view class="ellipsis">{{ s.qrCodeName }}</view>
                      <view>{{ s.location }}</view>
                      <view :class="['dxc', s.checkInState === '0' ? '' : 'active']">
                        {{ s.checkInState === '0' ? '待巡查' : '已巡查' }}
                      </view>
                    </view>
                    <view class="info-first-icon"></view>
                  </view>
                  <view class="info-first-context">
                    <view
                      class="info-first"
                      v-for="(jcx, sindex) in s.taskPointContentVos"
                      :key="sindex"
                    >
                      <view class="info-first-title">
                        <view
                          class="info-first-name"
                          style="padding: 8px; background-color: #f5f5f5"
                        >
                          <i style="font-size: 18px; padding-right: 8px">{{ sindex + 1 }}.</i>
                          {{ jcx.checkContent }}
                        </view>
                        <view class="info-first-icon" v-if="jcx.checkState && jcx.checkState != 0">
                          <wd-icon
                            :name="jcx.checkState == 1 ? 'check' : 'close'"
                            size="22px"
                            :style="{ color: jcx.checkState == 1 ? 'green' : '#F56C6C' }"
                          ></wd-icon>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 检查打卡记录 -->
        <view class="checkRecordBox">
          <wd-cell
            :style="{ paddingleft: 0 }"
            title="检查打卡记录"
            v-if="info.taskState !== '1' && info.isNeedClock === '1' && info.isClockInOne"
            is-link
            @click="goCheckRecord"
          ></wd-cell>
        </view>
      </view>
      <wd-status-tip v-else image="content" tip="暂无数据~" />
    </view>

    <view class="placeholder-bottom"></view>
    <view
      v-if="
        tabindex === '0' &&
        info.taskState !== '3' &&
        ((info.checkEndOperate === '2' && arrayContainsValue(fzrlist, userInfo.id)) ||
          arrayContainsValue(cyrsIds, userInfo.id))
      "
      class="fixed-bottom"
    >
      <template v-if="info.taskState === '2'">
        <!-- 开始任务 现场打卡 继续检查 结束任务 -->
        <wd-button
          v-if="info.isNeedClock === '1' && !info.isClockInOne"
          style="min-width: 0; border-radius: 4px"
          size="medium"
          @click="clockInHandle"
        >
          现场打卡
        </wd-button>
        <template v-else>
          <!-- 根据用户权限设置显示功能按钮 -->
          <wd-button type="info" size="large" @click="goEnd" :loading="endLoading" block>
            结束任务
          </wd-button>
          <wd-button type="primary" @click="scanCode" size="large" block>扫码检查</wd-button>
        </template>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useAppReceiveStore, useAppStore, useUserStore } from '@/store'
import SafetyNavbar from '../components/safety-navbar.vue'
import {
  getPointStatisticsAPI,
  PostcompletionTaskAPI,
  PostDevieStatisticsAPI,
  PostgetBuildAPI,
  PostgetDeviceAPI,
  PostgetDevieItemAPI,
  PostgetFloorAPI,
  PosthazardCheckDefaultConfigpointAPI,
  PoststartTaskAPI,
  selectBuildingListAPI,
  selectFloorListAPI,
  selectPointListAPI,
  taskDetail,
  getTaskClockInOne,
} from './featch'
import { wychScan } from './scanService'
import WsSocket from '@/utils/socket'
import { useMessage } from 'wot-design-uni'

const message = useMessage()

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const collapsevauel = ref(['itme1', 'itme'])
const pointList = ref([])
const taskId = ref('')
const info = ref<any>({})
const endLoading = ref(false)
// 查看更多检查项
const totalSHOW = ref(false)

const tabindex = ref()
onLoad((params) => {
  // 判断是否从本模块打开页面
  taskId.value = params.taskId
  tabindex.value = params.tabindex
  uni.setStorageSync('planId', params.planId)
  uni.setStorageSync('taskIdByindex', params.taskId)
})
onShow(() => {
  totalSHOW.value = false
  getDataList(taskId.value)

  uni.$on('scanFail', () => {
    uni.showToast({
      title: '扫码失败，请重新扫码',
      icon: 'none',
    })
  })
})

// 已巡查：只传checkInState=1
// 待巡查：只传checkInState=0
// 正常：只传checkState=1
// 异常：只传checkState=2

const checkState = ref('')

const checkInState = ref('')

const activeNum = ref(0)

function filterList(num: number) {
  activeNum.value = num
  switch (num) {
    case 0:
      checkState.value = ''
      checkInState.value = ''
      break
    case 1:
      checkState.value = ''
      checkInState.value = '1'
      break
    case 2:
      checkState.value = ''
      checkInState.value = '0'
      break
    case 3:
      checkState.value = '1'
      checkInState.value = ''
      break
    case 4:
      checkState.value = '2'
      checkInState.value = ''
      break
  }
  selectPointListGroupBuild()
}

// ============扫码===开始=================
const currentEnvInfo = useAppStore().appEnv

function scanCode() {
  if (currentEnvInfo.wychApp) {
    return wychScan.openSacn(taskId.value)
  }
  webUni.getEnv((env: any) => {
    if (env.miniprogram) {
      webUni.navigateTo({
        url: `/pages/common/scanPage?scanOri=${JSON.stringify({ taskId: taskId.value, isCheck: true })}`,
      })
    } else {
      webUni.postMessage({
        data: {
          action: 'scan',
          type: '',
          res: { taskId: taskId.value, isCheck: true },
        },
      })
    }
  })
}

const receiveStore = useAppReceiveStore()
watch(
  () => receiveStore.appData,
  (val) => {
    if (val && val.action !== 'shellScan') return
    console.log('隐患=>点位巡查接收扫码数据:::', JSON.parse(val.data))
    const { paramsObj } = JSON.parse(val.data)

    if (!paramsObj?.pointId || !pointIds.value.includes(paramsObj.pointId)) return scanPrompt()

    const queryString = Object.keys(paramsObj)
      .map((key) => `${key}=${paramsObj[key]}`)
      .join('&')
    uni.navigateTo({
      url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${encodeURIComponent(queryString)}`,
    })
  },
)

const $WS = new WsSocket({ topic: '/topic/scanEvent' })

const pointIds = computed(() => planNeedToCheck.value.map((item: any) => item.checkPointId) || [])
// 监听socket消息
$WS.onMessage = (msg) => {
  console.log('子应用 socket消息:::', msg)
  const { action, data } = msg
  if (action && action !== 'shellScan') return
  console.log('设备检查接收扫码数据:::', data)
  const { paramsObj } = data

  if (!paramsObj?.pointId || !pointIds.value.includes(paramsObj.pointId)) return scanPrompt()

  const queryString = Object.keys(paramsObj)
    .map((key) => `${key}=${paramsObj[key]}`)
    .join('&')

  uni.navigateTo({
    url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${encodeURIComponent(queryString)}`,
  })
}

const pointName = ref('')
function scanPrompt() {
  try {
    message
      .prompt({ title: '请输入巡查点位ID', inputValue: pointName.value })
      .then((res: any) => {
        // 输入正确
        if (res.value && pointIds.value.includes(res.value)) {
          return uni.navigateTo({
            url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${encodeURIComponent('pointId=' + res.value)}`,
          })
        }
        // 输入错误
        uni.showToast({ title: '巡查点位ID输入错误！', icon: 'none', duration: 1500 })
        setTimeout(() => scanPrompt(), 1500)
      })
      .catch(() => {})
  } catch (e) {
    console.error(e)
  }
}

onBeforeUnmount(() => {
  $WS.destroy()
})
// ============扫码===结束=================
// 负责人
const fzrlist = ref<any>([])
// 检查人
const cyrsIds = ref<any>([])
const totalNum = ref(0)
const planCheckPointList = ref<any>([]) // 扫码匹配用

// const num = ref(0)

const planNeedToCheck = ref<any[]>([])
function getDataList(id) {
  taskDetail(id).then(async (res: any) => {
    if (res.code === 'success') {
      const d = res.data
      planNeedToCheck.value = d.planCheckPointList // 扫码匹配用
      if (res.data.checkRange === '3') {
        planCheckPointList.value = d.planCheckPointList.slice(0, 3)
        totalNum.value = d.planCheckPointList.reduce((a, b, c) => {
          return c + 1
        })
        // // 获取当下任务的检查对象
        const unitList = d.unitIds.split(',').map((item, index) => {
          return {
            unitName: d.unitNames.split(',')[index],
            id: item,
          }
        })
        // // console.log(unitList, 'unitList.value')
        uni.setStorageSync('unitList', unitList)
        getPointStatistics()
        selectPointListGroupBuild()
      } else if (res.data.checkRange === '2') {
        devieStatistics()
        GetgetBuild()
      }
      info.value = d
      fzrlist.value = info.value.fzrsIds.split(',')
      cyrsIds.value = info.value.cyrsIds.split(',')

      if (info.value.taskState === '1') {
        // 更改任务状态
        if (fzrlist.value.includes(userInfo.id) || cyrsIds.value.includes(userInfo.id)) {
          PoststartTaskAPI(taskId.value, userInfo.id).then((res: any) => {
            if (res.code === 'success') {
              getDataList(taskId.value)
            }
          })
        }
      }

      // 是否需要现场打卡
      if (info.value.isNeedClock === '1') {
        const { isClock } = await getTaskClockInOneAPI({
          taskId: taskId.value,
          userId: userInfo.id,
        })
        if (isClock) {
          info.value.isClockInOne = isClock
        }
      }
    }
  })
}

// 请求最近一条打卡记录, 判断是否打卡
const getTaskClockInOneAPI = (params) => {
  try {
    return new Promise<any>((resolve, reject) => {
      getTaskClockInOne(params)
        .then((res: any) => {
          if (res.data?.id) {
            resolve({
              isClock: true,
            })
          } else {
            resolve({
              isClock: false,
            })
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

const defaulList = ref<any>([])
/* 获取楼栋 PostgetBuildAPI taskId --- */
const BuildList = ref<any>([])
function GetgetBuild() {
  PostgetBuildAPI(taskId.value)
    .then((res: any) => {
      BuildList.value = res.data
      defaulList.value = BuildList.value.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}
function selectPointListGroupBuild() {
  selectBuildingListAPI(taskId.value, checkInState.value, checkState.value)
    .then((res: any) => {
      pointList.value = res.data.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}
// 点位获取楼层
function getPointfool(item, index) {
  item.open = !item.open
  selectFloorListAPI(taskId.value, item.buildingId, checkInState.value, checkState.value)
    .then((res: any) => {
      pointList.value[index].lclist = res.data.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}
// 获取楼层
function getfool(item, index) {
  item.open = !item.open
  PostgetFloorAPI(item.buildId, taskId.value, item.unitId)
    .then((res: any) => {
      defaulList.value[index].lclist = res.data.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}
// 获取设备
function getshebei(item, lcindex, index) {
  item.open = !item.open
  // console.log('item, lcindex, index', item, lcindex, index)
  PostgetDeviceAPI(item.buildId, taskId.value, item.unitId, item.floorId)
    .then((res: any) => {
      // console.log(defaulList.value)
      defaulList.value[index].lclist[lcindex].shebeilist = res.data.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}

// 点位获取设备
function getPointshebei(item, lcindex, index) {
  item.open = !item.open
  selectPointListAPI(
    taskId.value,
    item.buildingId,
    item.floorId,
    checkInState.value,
    checkState.value,
  )
    .then((res: any) => {
      // console.log(defaulList.value)
      pointList.value[index].lclist[lcindex].shebeilist = res.data.map((item) => {
        return {
          ...item,
          open: false,
        }
      })
    })
    .finally(() => {})
}

// 获取设备检查项
function getjcx(sid, index, lcindex, sindex, item) {
  item.open = !item.open
  PostgetDevieItemAPI(sid, taskId.value)
    .then((res: any) => {
      defaulList.value[index].lclist[lcindex].shebeilist[sindex].jcxlist = res.data
    })
    .finally(() => {})
}
const defaultNum = ref(0)
function getPointList() {
  PosthazardCheckDefaultConfigpointAPI({
    checkTableType: 'point',
    pageNo: 1,
    pageSize: 9999,
    unitId: userInfo.topUnitId,
  }).then((res: any) => {
    defaultNum.value = res.data.total
  })
}
getPointList()

/* 获取设备统计情况 PostDevieStatisticsAPI */
const devieStatisticsinfo = ref<any>({})
function devieStatistics() {
  PostDevieStatisticsAPI(taskId.value)
    .then((res: any) => {
      devieStatisticsinfo.value = res.data
    })
    .finally(() => {})
}

function getPointStatistics() {
  getPointStatisticsAPI(taskId.value)
    .then((res: any) => {
      devieStatisticsinfo.value = res.data
    })
    .finally(() => {})
}
/* 结束任务操作 */
function goEnd() {
  // 判断当前用户是否是检查负责人，只有负责人能结束任务
  // if (info.value.checkEndOperate === '1' && !arrayContainsValue(fzrlist.value, userInfo.id)) {
  //   uni.showToast({
  //     icon: 'none',
  //     title: '您没有权限哦！负责人才能结束任务',
  //   })
  //   return
  // }
  // // console.log('结束')
  uni.showModal({
    title: '提示',
    content: '确认要结束任务吗?结束任务后会上报检查出的隐患',
    success: function (res) {
      if (res.confirm) {
        // console.log('用户点击确定')
        endLoading.value = true
        // 在这里执行确定后的操作
        PostcompletionTaskAPI(taskId.value, userInfo.id)
          .then((res: any) => {
            if (res.code === 'success') {
              uni.showToast({
                icon: 'none',
                title: res.message,
              })
              endLoading.value = false
              getDataList(taskId.value)
            } else if (res.code === 'error') {
              uni.showToast({
                icon: 'none',
                title: res.message,
              })
              // // 2秒后执行
              // setTimeout(() => {
              //   goBack()
              // }, 2000)
            } else {
              uni.showModal({
                title: '提示',
                content: res.message,
                showCancel: false, // 是否显示取消按钮
                confirmText: '我知道了', // 确认按钮的文字，默认为"确定"
                success: function (res) {
                  if (res.confirm) {
                    uni.navigateBack()
                    // 确定按钮的逻辑
                  }
                },
              })
            }
          })
          .finally(() => {
            endLoading.value = false
          })
      } else if (res.cancel) {
        // 在这里执行取消后的操作
        endLoading.value = false
      }
    },
  })
}

// // 取消上报
const goBack = () => {
  uni.navigateBack()
}
function arrayContainsValue(arr, value: string) {
  if (arr) {
    return arr.includes(value)
  } else {
    return false
  }
}
// 点击查看全部
function geloadMore() {
  totalSHOW.value = true
  planCheckPointList.value = info.value.planCheckPointList
  // console.log(info.value.planCheckPointList, '@click="geloadMore()"')
}
// 跳转安全检查表---任务详情获取
function goCheckTable(checkTableId) {
  uni.navigateTo({
    url: `/pages/task/productionInspect/equipentcheckItme?taskId=${taskId.value}&checkRange=${info.value.checkRange}&checkTableId=${checkTableId}`,
  })
}

// 重新打卡/现场打卡
function clockInHandle() {
  const url = currentEnvInfo.wychApp
    ? `/pages/task/clockIn2?taskId=${taskId.value}&lat1=${info.value.unitPointY}&lon1=${info.value.unitPointX}&range=${info.value.printRange}`
    : `/pages/task/clockIn?taskId=${taskId.value}&lat1=${info.value.unitPointY}&lon1=${info.value.unitPointX}&range=${info.value.printRange}`
  uni.navigateTo({
    url,
  })
}

// 打卡记录页面
function goCheckRecord() {
  const fzrsIds = info.value.fzrsIds.split(',')
  const _id = fzrsIds.includes(userInfo.id) ? '' : userInfo.id
  uni.navigateTo({
    url: `/pages/task/checkRecord?taskId=${taskId.value}&userinfoId=${_id}`,
  })
}
</script>

<style lang="scss" scoped>
.ellipsis {
  width: 45%;
  overflow: hidden;
  /* 确保超出的内容会被隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号显示超出的文字 */
  white-space: nowrap;
  /* 防止文字换行 */
}

.ellipsis1 {
  width: 40%;
  overflow: hidden;
  /* 确保超出的内容会被隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号显示超出的文字 */
  white-space: nowrap;
  /* 防止文字换行 */
}

.num-css {
  font-size: 16px;
  font-weight: bold;
}

.cell-1 {
  padding: 8px;
  border-bottom: 0.5px solid #ccc;
}

.ld {
  padding: 8px;
  border-bottom: 0.3px solid #ccc;
}

.cfontsize {
  font-size: 0.875rem;
  color: #232323;
}

.sizeColor {
  color: #0052d9;
}

.dw-info {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 0.3px solid #ccc;
}

.j-spacebetween {
  display: flex;
  justify-content: space-between;
  padding: 0.6rem 0.4rem;
  border-bottom: 1px solid #e8e8e8;
}

.j-spacebetween :last-child {
  border: none;
}

// .j-spacebetween .dw-jcnr1 {
//   width: 80%;
//   margin: auto;
// }

.jcb {
  padding: 0.75rem 0;
  margin-top: 0.375rem;
  border-top: 0.0313rem solid #ccc;
  border-bottom: 0.0313rem solid #ccc;

  .itme1 {
    display: flex;
    align-items: center;
    padding-left: 1rem;
    margin-top: 0.375rem;

    .titleicon {
      width: 0.1875rem;
      height: 1.125rem;
      margin-right: 0.75rem;
      background-color: #0052d9;
      border-radius: 0.3125rem;
    }
  }
}

.statistics {
  height: 64px;
  padding: 0 6px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 6px;
  font-size: 0.875rem;
  .active {
    background-color: rgba(0, 82, 217, 0.1);
  }
  .word-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.placeholder-bottom {
  width: 100%;
  height: 3.375rem;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding-top: 0;
    // padding: 0 1.6rem 1.3rem 1.125rem !important;
  }

  .wd-collapse-item__wrapper {
    height: auto;
  }
}

.list-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 15px auto;

  .list-item-context {
    width: calc(100% - 100px);
    font-size: 14px;
    color: rgb(75, 75, 75);
  }

  .list-item-button {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 80px;
  }
}

.info-first {
  width: 100%;
  margin: 10px auto;
  color: rgb(75, 75, 75);

  .info-first-title {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .dxc {
      padding: 2px 6px;
      background: #f59a23;
      border-radius: 2px 2px 2px 2px;
      font-size: 12px;
      color: #ffffff;
      .active {
        background-color: #00a720;
      }
    }
  }

  .info-first-context {
    margin-top: 10px;
    margin-left: 10px;
  }
}
.checkRecordBox {
  margin: 10px 0;
  border-top: 1px solid #e8e8e8;

  ::v-deep {
    .wd-cell {
      padding-left: 0;
      .wd-cell__wrapper {
        padding-right: 0;
      }
    }
  }
}
</style>
