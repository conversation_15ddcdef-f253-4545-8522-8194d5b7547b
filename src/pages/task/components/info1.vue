<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <view class="fasticon"></view>
        <view>检查信息</view>
      </view>
      <view class="infoItme" v-if="info1Itme.checkContent">
        <view class="infokey">检查类别</view>
        <view class="infovalue">{{ info1Itme.checkContent }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.checkDetail">
        <view class="infokey">检查内容</view>
        <view class="infovalue">{{ info1Itme.checkDetail }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.legalBasis">
        <view class="infokey">检查依据</view>
        <view class="infovalue">{{ info1Itme.legalBasis }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.complianceRequirements">
        <view class="infokey">险查标准</view>
        <view class="infovalue">{{ info1Itme.complianceRequirements }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.legalText">
        <view class="infokey">法律原文</view>
        <view class="infovalue">{{ info1Itme.legalText }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.legalLiability">
        <view class="infokey">法律责任</view>
        <view class="infovalue">{{ info1Itme.legalLiability }}</view>
      </view>
      <view class="infoItme" v-if="info1Itme.checkAreaFileList.length > 0">
        <view class="infokey" style="min-width: 4.25rem">图例</view>
        <view
          class="infovalue"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start"
        >
          <view class="fileList" v-for="(item, index) in info1Itme.checkAreaFileList" :key="index">
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(getFileURL(item.fileUrl))"
            />
            <!-- <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true" /> -->
          </view>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
// import { useAppStore, usefileConfigStore } from '@/store'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'
defineProps({
  info1Itme: {
    type: Object,
  },
})
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
// const usefileStore = usefileConfigStore()
// const VITE_PREVIEW_BASEURL = ref()
// const currentEnvInfo = useAppStore().appEnv
// if (!currentEnvInfo.wychApp) {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
// } else {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
// }
// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 4.25rem;
      color: #7f7f7f;
    }

    .infovalue {
      width: calc(100% - 70px);
      text-align: left;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
