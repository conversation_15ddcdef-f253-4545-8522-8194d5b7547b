<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infoItme">
        <view class="infokey">隐患描述</view>
        <view class="infovalue">{{ info1Itme.hazardDescribe }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患分类</view>
        <!-- essentialFactorClassName -->
        <view class="infovalue">
          {{ info1Itme.essentialFactorClassFullName || info1Itme.essentialFactorClassName }}
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患级别</view>
        <view class="infovalue">{{ info1Itme.hazardGradeName }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查项</view>
        <view class="infovalue">{{ info1Itme.inspectionItem }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查内容</view>
        <view class="infovalue">{{ info1Itme.inspectionDescribe }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律依据</view>
        <view class="infovalue">{{ info1Itme.inspectionItemBasis }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">合规要求</view>
        <view class="infovalue">{{ info1Itme.inspectionAsk }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律原文</view>
        <view class="infovalue">{{ info1Itme.legalText }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律责任</view>
        <view class="infovalue">{{ info1Itme.legalLiability }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 4.25rem">正确图例</view>
        <view
          class="infovalue"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start"
        >
          <view
            class="fileList"
            v-for="(item, index) in info1Itme.commonFiles?.filter(
              (item) => item.fileCategory == '1',
            )"
            :key="index"
          >
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(getFileURL(item.fileUrl))"
            />
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true"
            /> -->
          </view>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 4.25rem">错误图例</view>
        <view
          class="infovalue"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start"
        >
          <view
            class="fileList"
            v-for="(item, index) in info1Itme.commonFiles?.filter(
              (item) => item.fileCategory == '2',
            )"
            :key="index"
          >
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(getFileURL(item.fileUrl))"
            />
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true"
            /> -->
          </view>
        </view>
      </view>
    </view>
    <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'
import { hazardEssentialFactorClassItem } from '../type'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'
defineProps({
  info1Itme: {
    type: Object,
  },
})

// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
// const usefileStore = usefileConfigStore()
// const VITE_PREVIEW_BASEURL = ref()
// const currentEnvInfo = useAppStore().appEnv
// if (!currentEnvInfo.wychApp) {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
// } else {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
// }

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  // padding-bottom: 1.125rem;
  padding-bottom: 4rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    // display: flex;
    // align-items: flex-start;
    // justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 4.25rem;
      margin-right: 3rem;
      margin-bottom: 0.5rem;
      color: #7f7f7f;
    }
  }

  .hideDangerItme {
    width: 90%;
    padding: 0.5rem;
    margin: auto;
    font-size: 0.875rem;
    background-color: #ebeef5;
    border-radius: 0.3125rem;

    .infokey {
      // min-width: 6.25rem;
      margin-right: 3rem;
      margin-bottom: 0.5rem;
      color: #7f7f7f;

      .infotag {
        height: 1.5625rem;
        padding: 0 0.625rem;
        line-height: 1.5625rem;
        color: white;
        background-color: #ff9908;
        border-radius: 1rem;
      }
    }
  }

  .fileList {
    width: 4.125rem;
    height: 4.125rem;
    margin: 0.5rem;
    text-align: center;
    border-radius: 0.375rem;
  }
}
</style>
