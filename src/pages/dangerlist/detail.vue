<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患详情',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患详情"></SafetyNavbar>
  <view class="container">
    <view style="padding-bottom: 4rem">
      <info1 :info1Itme="hazardInventoryDetail"></info1>
      <view style="background-color: white">
        <customTabs
          :tabs="tabs"
          :activeIndex="activeIndex"
          @handleClick="handleChange"
        ></customTabs>
        <!-- <div class="tableitme" v-if="activeIndex === 0 && hazardSource"> -->
        <div class="tableitme" v-if="activeIndex === 0">
          <info2 :info2Itme="hazardInventoryDetail"></info2>
        </div>
        <div class="tableitme" v-if="activeIndex === 1">
          <changerecord ref="updaterecord" :disposeId="disposeId"></changerecord>
        </div>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="fixed-bottom" v-if="isdispose !== ''">
      <wd-button
        type="info"
        size="large"
        v-if="
          (arrayContainsValue(userinfo.roleCodes, 'expert') ||
            arrayContainsValue(userinfo.roleCodes, 'ahbAdmin') ||
            arrayContainsValue(userinfo.roleCodes, 'admin') ||
            arrayContainsValue(userinfo.roleCodes, 'safetyCommitteePerson')) &&
          hazardInventoryDetail?.disposeState !== 1 &&
          hazardInventoryDetail?.reviewState === 1
        "
        @click="saveUrgeRecord"
        block
      >
        催促
      </wd-button>
      <wd-button
        type="primary"
        size="large"
        @click="ReviewCheck()"
        block
        v-if="
          (arrayContainsValuebyuser(hazardInventoryDetail?.reformUsers, userinfo.id) ||
            arrayContainsValue(userinfo.roleCodes, 'controlRoomDutyPerson') ||
            arrayContainsValue(userinfo.roleCodes, 'workAreaSafetyPerson')) &&
          hazardInventoryDetail?.disposeState !== 1 &&
          hazardInventoryDetail?.reviewState === 1
        "
      >
        隐患整改
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import info1 from './components/info1.vue'
import customTabs from '../components/custom-Tabs.vue'
import changerecord from './components/change_record.vue'
import info2 from './components/info2.vue'
import { useUserStore } from '@/store'
import {
  postHazardInventoryDetailAPI,
  posthazardMegerqueryDetailAPI,
  posthazardPlanTaskEventAPI,
  postsaveUrgeRecordAPI,
} from './featch'
import { debounce } from '@/utils'
// import { detailFrom } from './type'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
function arrayContainsValue(arr, value: string) {
  // // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是整改人===========arr')
  if (arr?.length > 0 && arr !== null) {
    return arr.includes(value)
  } else {
    return false
  }
}
function arrayContainsValuebyuser(arr, value: string) {
  // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是整改人===========arr')
  if (arr?.length > 0 && arr !== undefined) {
    return arr.some((item) => item.reformUserId === value)
  } else {
    return false
  }
}
const updaterecord = ref()
const disposeId = ref<any>('')
const isdispose = ref<any>('') // 是否可以进行操作
const paramasData = ref<any>({
  id: '',
  flag: '',
})

onLoad((params) => {
  // console.log('params = ', params)
  disposeId.value = params.disposeId
  paramasData.value.flag = params.flag
  paramasData.value.id = params.id
  if (params.isdispose) {
    isdispose.value = params.isdispose
  }
})
onShow(() => {
  gethazardMegerqueryDetail()
  updaterecord.value?.updaterecordAPI()
})
const tabs = ref([])
const activeIndex = ref(0)
function handleChange(event) {
  if ([1, 3, 14, 15, 16, 17, 18, 19, 20].includes(hazardInventoryDetail.value.hazardSource)) {
    activeIndex.value = 1
  } else {
    activeIndex.value = event
  }
}
const hazardInventoryDetail = ref<any>({})
function gethazardMegerqueryDetail() {
  posthazardMegerqueryDetailAPI(paramasData.value)
    .then((res: any) => {
      hazardInventoryDetail.value = res.data
      if ([1, 3, 14, 15, 16, 17, 18, 19, 20].includes(hazardInventoryDetail.value.hazardSource)) {
        tabs.value = ['整改记录']
        activeIndex.value = 1
      } else {
        tabs.value = ['检查信息', '整改记录']
      }
    })
    .finally(() => {
      // console.log(111)
    })
}

/*  催促信息 需要从登录人获取 */
const operatorModel = ref<any>({
  disposeId: disposeId.value, // 处置id
  eventType: '4', // 事件类型
  operatorId: userinfo.id, // 操作人ID
  operatorName: userinfo.userName, // 操作人姓名
  operatorTel: userinfo.userTelphone, // 操作联系方式
  subCenterCode: userinfo.zhId, // 运营中心编码
})

/* 催促操作 */
const saveUrgeRecord = debounce(() => {
  operatorModel.value.disposeId = disposeId.value
  uni.showModal({
    title: '提示',
    content: '请确认是否要进行催促?',
    success: function (res) {
      if (res.confirm) {
        // 在这里执行确定后的操作
        // console.log(JSON.stringify(operatorModel.value))
        postsaveUrgeRecordAPI(operatorModel.value)
          .then((res: any) => {
            uni.showToast({
              icon: 'none',
              title: res.message,
            })
            updaterecord.value?.updaterecordAPI()
          })
          .finally(() => {})
      } else if (res.cancel) {
        // 在这里执行取消后的操作
      }
    },
  })
}, 500)
//* 整改操作 */
function ReviewCheck() {
  // 根据权限跳转不同页面
  uni.navigateTo({
    url: `/pages/dangerlist/ReviewCheck?id=${hazardInventoryDetail.value.disposeId}&randId=${hazardInventoryDetail.value.id}`,
  })
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .tabs-header {
    display: flex;
    padding-bottom: 0px;
    color: #969696;
    background-image: none;
    border-bottom: 0.5px solid #ebebeb;
  }

  .tabs-header-item {
    padding: 10px 10px 0px 10px;
  }

  .tabs-header-active {
    color: #0256ff;
  }

  .tabs-header-item-active {
    width: 40px;
    height: 4px;
    margin-top: 6px;
    background-color: #0256ff;
    border-radius: 1px;
  }
}

body {
  background-color: #f9f9f9 !important;
}

.sheetbom {
  display: flex;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.tableitme {
  padding: 0.52rem 1.04rem;
}
</style>
