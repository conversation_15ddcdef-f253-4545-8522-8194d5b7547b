<!doctype html>
<html build-time="%BUILD_TIME%">

<head>
  <meta charset="UTF-8" />
  <!-- aqsc/v1/hazard/h5 -->
  <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
  <!-- <link rel="stylesheet" crossorigin href="./assets/index-B315M65T.css" /> -->
  <link rel="stylesheet" href="./static/mapEnginePackageCSS.css" />
  <!-- <link
      rel="stylesheet"
      href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree_CSS&wgId=67"
    /> -->
  <script type="text/javascript"
    src="https://api.map.baidu.com/api?v=1.0&&type=webgl&ak=oDdBeR7xhecYsAJJfshlqeJdEsJqKDTR"></script>
  <!-- <script
      charset="utf-8"
      src="https://mapapi.qq.com/web/mapComponents/geoLocation/v/geolocation.min.js"
    ></script> -->
  <script src="https://map.qq.com/api/js?v=2.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77"></script>
  <script>
    var coverSupport =
      'CSS' in window &&
      typeof CSS.supports === 'function' &&
      (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') +
      '" />',
    )
  </script>
  <title>隐患排查治理</title>
  <!--preload-links-->
  <!--app-context-->
</head>
<!-- <script src="./static/vconsole.min.js"></script> -->
<script>
  // var vConsole = new VConsole()
  // console.log('Hello world')
</script>

<body>
  <div id="app"><!--app-html--></div>
</body>
<script type="module" src="/src/main.ts"></script>
<!-- <script
    type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=IndoorThree&wgId=67"
  ></script> -->
<script type="text/javascript" src="./static/jweixin-1.4.0.js"></script>
<script type="text/javascript" src="./static/mapEnginePackage.js"></script>
<script type="text/javascript" src="./static/uni.webview.1.5.5.js"></script>
<script type="text/javascript" src="./static/waltz.min.js"></script>
</html>
